package graph

import (
	"context"
	"fmt"

	"crypto-bubble-map-be/internal/domain/entity"
	"go.uber.org/zap"
)

// WalletNetwork resolves wallet network queries
func (r *queryResolver) WalletNetwork(ctx context.Context, input entity.WalletNetworkInput) (*entity.WalletNetwork, error) {
	r.logger.Info("Resolving wallet network query",
		zap.String("address", input.Address),
		zap.Int("depth", input.Depth),
		zap.String("network_id", input.NetworkID),
	)

	// Check cache first
	var cachedNetwork entity.WalletNetwork
	if err := r.cache.GetWalletNetwork(ctx, input.Address, input.Depth, &cachedNetwork); err == nil {
		r.logger.Debug("Returning cached wallet network", zap.String("address", input.Address))
		return &cachedNetwork, nil
	}

	// Get from repository
	network, err := r.walletRepo.GetWalletNetwork(ctx, &input)
	if err != nil {
		r.logger.Error("Failed to get wallet network",
			zap.String("address", input.Address),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get wallet network: %w", err)
	}

	// Cache the result
	if err := r.cache.SetWalletNetwork(ctx, input.Address, input.Depth, network); err != nil {
		r.logger.Warn("Failed to cache wallet network", zap.Error(err))
	}

	return network, nil
}

// Wallet resolves single wallet queries
func (r *queryResolver) Wallet(ctx context.Context, address string) (*entity.Wallet, error) {
	r.logger.Info("Resolving wallet query", zap.String("address", address))

	wallet, err := r.walletRepo.GetWallet(ctx, address)
	if err != nil {
		r.logger.Error("Failed to get wallet",
			zap.String("address", address),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get wallet: %w", err)
	}

	return wallet, nil
}

// WalletRiskScore resolves wallet risk score queries
func (r *queryResolver) WalletRiskScore(ctx context.Context, address string) (*entity.RiskScore, error) {
	r.logger.Info("Resolving wallet risk score query", zap.String("address", address))

	// Check cache first
	var cachedScore entity.RiskScore
	if err := r.cache.GetRiskScore(ctx, address, &cachedScore); err == nil {
		r.logger.Debug("Returning cached risk score", zap.String("address", address))
		return &cachedScore, nil
	}

	// Get from repository
	riskScore, err := r.walletRepo.GetRiskScore(ctx, address)
	if err != nil {
		r.logger.Error("Failed to get risk score",
			zap.String("address", address),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get risk score: %w", err)
	}

	// Cache the result
	if err := r.cache.SetRiskScore(ctx, address, riskScore); err != nil {
		r.logger.Warn("Failed to cache risk score", zap.Error(err))
	}

	return riskScore, nil
}

// PairwiseTransactions resolves pairwise transaction queries
func (r *queryResolver) PairwiseTransactions(ctx context.Context, walletA string, walletB string, limit *int, offset *int, filters *entity.TransactionFilters) (*entity.PairwiseTransactionResult, error) {
	r.logger.Info("Resolving pairwise transactions query",
		zap.String("wallet_a", walletA),
		zap.String("wallet_b", walletB),
	)

	// Set defaults
	if limit == nil {
		defaultLimit := 100
		limit = &defaultLimit
	}
	if offset == nil {
		defaultOffset := 0
		offset = &defaultOffset
	}

	result, err := r.transactionRepo.GetPairwiseTransactions(ctx, walletA, walletB, int64(*limit), int64(*offset), filters)
	if err != nil {
		r.logger.Error("Failed to get pairwise transactions",
			zap.String("wallet_a", walletA),
			zap.String("wallet_b", walletB),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get pairwise transactions: %w", err)
	}

	return result, nil
}

// MoneyFlowData resolves money flow data queries
func (r *queryResolver) MoneyFlowData(ctx context.Context, walletAddress string, filters entity.MoneyFlowFilters) (*entity.MoneyFlowData, error) {
	r.logger.Info("Resolving money flow data query", zap.String("wallet_address", walletAddress))

	data, err := r.transactionRepo.GetMoneyFlowData(ctx, walletAddress, &filters)
	if err != nil {
		r.logger.Error("Failed to get money flow data",
			zap.String("wallet_address", walletAddress),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get money flow data: %w", err)
	}

	return data, nil
}

// DashboardStats resolves dashboard statistics queries
func (r *queryResolver) DashboardStats(ctx context.Context, networkID *string) (*entity.DashboardStats, error) {
	r.logger.Info("Resolving dashboard stats query", zap.Stringp("network_id", networkID))

	// Create cache key
	cacheKey := "all"
	if networkID != nil {
		cacheKey = *networkID
	}

	// Check cache first
	var cachedStats entity.DashboardStats
	if err := r.cache.GetDashboardStats(ctx, cacheKey, &cachedStats); err == nil {
		r.logger.Debug("Returning cached dashboard stats", zap.String("network_id", cacheKey))
		return &cachedStats, nil
	}

	// Get from repository
	stats, err := r.networkRepo.GetDashboardStats(ctx, networkID)
	if err != nil {
		r.logger.Error("Failed to get dashboard stats",
			zap.Stringp("network_id", networkID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}

	// Cache the result
	if err := r.cache.SetDashboardStats(ctx, cacheKey, stats); err != nil {
		r.logger.Warn("Failed to cache dashboard stats", zap.Error(err))
	}

	return stats, nil
}

// WalletRankings resolves wallet ranking queries
func (r *queryResolver) WalletRankings(ctx context.Context, category entity.RankingCategory, networkID *string, limit *int, offset *int) (*entity.WalletRankingResult, error) {
	r.logger.Info("Resolving wallet rankings query",
		zap.String("category", string(category)),
		zap.Stringp("network_id", networkID),
	)

	// Set defaults
	if limit == nil {
		defaultLimit := 100
		limit = &defaultLimit
	}
	if offset == nil {
		defaultOffset := 0
		offset = &defaultOffset
	}

	// Check cache first
	var cachedRankings entity.WalletRankingResult
	if err := r.cache.GetWalletRankings(ctx, string(category), &cachedRankings); err == nil {
		r.logger.Debug("Returning cached wallet rankings", zap.String("category", string(category)))
		return &cachedRankings, nil
	}

	// Get from repository
	result, err := r.walletRepo.GetWalletRankings(ctx, category, networkID, *limit, *offset)
	if err != nil {
		r.logger.Error("Failed to get wallet rankings",
			zap.String("category", string(category)),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get wallet rankings: %w", err)
	}

	// Cache the result
	if err := r.cache.SetWalletRankings(ctx, string(category), result); err != nil {
		r.logger.Warn("Failed to cache wallet rankings", zap.Error(err))
	}

	return result, nil
}

// Networks resolves network list queries
func (r *queryResolver) Networks(ctx context.Context) ([]entity.NetworkInfo, error) {
	r.logger.Info("Resolving networks query")

	networks, err := r.networkRepo.GetNetworks(ctx)
	if err != nil {
		r.logger.Error("Failed to get networks", zap.Error(err))
		return nil, fmt.Errorf("failed to get networks: %w", err)
	}

	return networks, nil
}

// NetworkStats resolves network statistics queries
func (r *queryResolver) NetworkStats(ctx context.Context, networkID string) (*entity.NetworkStats, error) {
	r.logger.Info("Resolving network stats query", zap.String("network_id", networkID))

	// Check cache first
	var cachedStats entity.NetworkStats
	if err := r.cache.GetNetworkStats(ctx, networkID, &cachedStats); err == nil {
		r.logger.Debug("Returning cached network stats", zap.String("network_id", networkID))
		return &cachedStats, nil
	}

	// Get from repository
	stats, err := r.networkRepo.GetNetworkStats(ctx, networkID)
	if err != nil {
		r.logger.Error("Failed to get network stats",
			zap.String("network_id", networkID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get network stats: %w", err)
	}

	// Cache the result
	if err := r.cache.SetNetworkStats(ctx, networkID, stats); err != nil {
		r.logger.Warn("Failed to cache network stats", zap.Error(err))
	}

	return stats, nil
}

// NetworkRankings resolves network ranking queries
func (r *queryResolver) NetworkRankings(ctx context.Context, limit *int) ([]entity.NetworkRanking, error) {
	r.logger.Info("Resolving network rankings query")

	// Set default
	if limit == nil {
		defaultLimit := 20
		limit = &defaultLimit
	}

	rankings, err := r.networkRepo.GetNetworkRankings(ctx, *limit)
	if err != nil {
		r.logger.Error("Failed to get network rankings", zap.Error(err))
		return nil, fmt.Errorf("failed to get network rankings: %w", err)
	}

	return rankings, nil
}

// SearchWallets resolves wallet search queries
func (r *queryResolver) SearchWallets(ctx context.Context, query string, limit *int) ([]entity.WalletSearchResult, error) {
	r.logger.Info("Resolving search wallets query", zap.String("query", query))

	// Set default
	if limit == nil {
		defaultLimit := 20
		limit = &defaultLimit
	}

	results, err := r.walletRepo.SearchWallets(ctx, query, *limit)
	if err != nil {
		r.logger.Error("Failed to search wallets",
			zap.String("query", query),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to search wallets: %w", err)
	}

	return results, nil
}

// AskAI resolves AI assistant queries
func (r *queryResolver) AskAI(ctx context.Context, question string, aiContext *entity.AIContext, walletAddress *string) (*entity.AIResponse, error) {
	r.logger.Info("Resolving AI query",
		zap.String("question", question),
		zap.Stringp("wallet_address", walletAddress),
	)

	response, err := r.aiRepo.AskAI(ctx, question, aiContext, walletAddress)
	if err != nil {
		r.logger.Error("Failed to get AI response",
			zap.String("question", question),
			zap.Error(err),
		)
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	return response, nil
}

// Query returns the query resolver
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type queryResolver struct{ *Resolver }
