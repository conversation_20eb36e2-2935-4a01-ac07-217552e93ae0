version: '3.8'

services:
  # Neo4J Database
  neo4j:
    image: neo4j:5.15-community
    container_name: crypto-bubble-map-neo4j
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    environment:
      NEO4J_AUTH: neo4j/password
      NEO4J_PLUGINS: '["apoc"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*
      NEO4J_dbms_memory_heap_initial__size: 1G
      NEO4J_dbms_memory_heap_max__size: 4G
      NEO4J_dbms_memory_pagecache_size: 1G
      NEO4J_dbms_transaction_concurrent_maximum: 100
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - crypto-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7474/browser/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Crypto Bubble Map Indexer
  indexer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crypto-bubble-map-indexer
    ports:
      - "8080:8080"  # Health check
      - "9090:9090"  # Metrics
    environment:
      # Application Configuration
      APP_ENV: development
      LOG_LEVEL: info
      HTTP_PORT: 8080
      WORKER_POOL_SIZE: 20
      BATCH_SIZE: 100

      # NATS Configuration - Connect to external NATS from ethereum-raw-data-crawler
      NATS_URL: ${NATS_URL:-nats://ethereum-nats:4222}
      NATS_STREAM_NAME: TRANSACTIONS
      NATS_SUBJECT_PREFIX: transactions
      NATS_CONSUMER_GROUP: bubble-map-indexer
      NATS_CONNECT_TIMEOUT: 10s
      NATS_RECONNECT_ATTEMPTS: 5
      NATS_RECONNECT_DELAY: 2s
      NATS_MAX_PENDING_MESSAGES: 10000
      NATS_ENABLED: true

      # Neo4J Configuration
      NEO4J_URI: ${NEO4J_URI:-neo4j://neo4j:7687}
      NEO4J_USERNAME: ${NEO4J_USERNAME:-neo4j}
      NEO4J_PASSWORD: ${NEO4J_PASSWORD:-password}
      NEO4J_DATABASE: ${NEO4J_DATABASE:-neo4j}
      NEO4J_CONNECT_TIMEOUT: 10s
      NEO4J_MAX_CONNECTION_POOL_SIZE: 100
      NEO4J_CONNECTION_ACQUISITION_TIMEOUT: 60s

      # Health Check Configuration
      HEALTH_CHECK_INTERVAL: 30s
      HEALTH_CHECK_TIMEOUT: 5s

      # Metrics Configuration
      METRICS_ENABLED: true
      METRICS_PORT: 9090
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - crypto-network
      - ethereum-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

networks:
  crypto-network:
    driver: bridge
  ethereum-network:
    external: true
    name: ethereum-raw-data-crawler_ethereum-network