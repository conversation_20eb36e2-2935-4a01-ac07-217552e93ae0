# Ethereum RPC Configuration - Update with your API keys
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
ETHEREUM_WS_URL=wss://mainnet.infura.io/ws/v3/YOUR_PROJECT_ID
START_BLOCK_NUMBER=0

# MongoDB Configuration - Uses Docker container
MONGO_URI=**********************************************************************************************************************************************************************************************************************************************************************************
MONGO_DATABASE=ethereum_raw_data
MONGO_CONNECT_TIMEOUT=15s
MONGO_MAX_POOL_SIZE=10

# Application Configuration
APP_PORT=8080
APP_ENV=production
LOG_LEVEL=info

# Crawler Configuration - Conservative for stability
BATCH_SIZE=1
CONCURRENT_WORKERS=1
RETRY_ATTEMPTS=3
RETRY_DELAY=3s

# Batch Upsert Configuration - Enable duplicate handling
CRAWLER_USE_UPSERT=true
CRAWLER_UPSERT_FALLBACK=true

# Rate limiting for Ethereum API
ETHEREUM_RATE_LIMIT=1s
ETHEREUM_REQUEST_TIMEOUT=120s
ETHEREUM_SKIP_RECEIPTS=true

# Scheduler Configuration - Real-time mode with polling fallback
SCHEDULER_MODE=realtime
SCHEDULER_ENABLE_REALTIME=true
SCHEDULER_ENABLE_POLLING=true
SCHEDULER_POLLING_INTERVAL=3s
SCHEDULER_FALLBACK_TIMEOUT=30s
SCHEDULER_RECONNECT_ATTEMPTS=5
SCHEDULER_RECONNECT_DELAY=5s
SCHEDULER_MAX_RETRIES=3
SCHEDULER_SKIP_DURATION=30s

# WebSocket Configuration
WEBSOCKET_RECONNECT_ATTEMPTS=5
WEBSOCKET_RECONNECT_DELAY=5s
WEBSOCKET_PING_INTERVAL=30s

# GraphQL Configuration
GRAPHQL_ENDPOINT=/graphql
GRAPHQL_PLAYGROUND=false

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30s

# NATS JetStream Configuration (Optional - disabled for Docker setup)
NATS_URL=nats://localhost:4222
NATS_STREAM_NAME=TRANSACTIONS
NATS_SUBJECT_PREFIX=transactions
NATS_CONNECT_TIMEOUT=10s
NATS_RECONNECT_ATTEMPTS=5
NATS_RECONNECT_DELAY=2s
NATS_MAX_PENDING_MESSAGES=1000
NATS_ENABLED=false
