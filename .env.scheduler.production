# =============================================================================
# PRODUCTION CONFIGURATION FOR ETHEREUM SCHEDULER
# =============================================================================
# This file contains optimized settings for production deployment with
# self-hosted MongoDB on VPS

# =============================================================================
# ETHEREUM CONFIGURATION
# =============================================================================

# Ethereum RPC endpoints (replace with your actual endpoints)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
ETHEREUM_WS_URL=wss://mainnet.infura.io/ws/v3/YOUR_PROJECT_ID

# Starting block number (set to latest for real-time crawling)
START_BLOCK_NUMBER=latest

# Request timeout (increased for stability)
ETHEREUM_REQUEST_TIMEOUT=120s

# Rate limiting between API requests (conservative for stability)
ETHEREUM_RATE_LIMIT=1s

# Skip transaction receipts to reduce load
ETHEREUM_SKIP_RECEIPTS=true

# =============================================================================
# MONGODB CONFIGURATION (OPTIMIZED FOR SELF-HOSTED)
# =============================================================================

# MongoDB connection URI with optimized parameters for self-hosted deployment
MONGO_URI=*****************************************************************************************************************************************************************************************************************************************************************

# MongoDB database name
MONGO_DATABASE=ethereum_raw_data

# MongoDB connection timeout (increased for stability)
MONGO_CONNECT_TIMEOUT=15s

# MongoDB connection pool size (optimized for VPS)
MONGO_MAX_POOL_SIZE=50

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application environment
APP_ENV=production

# Log level (info for production, debug for troubleshooting)
LOG_LEVEL=info

# Application port
APP_PORT=8080

# =============================================================================
# CRAWLER CONFIGURATION (CONSERVATIVE FOR STABILITY)
# =============================================================================

# Batch size (small for stability)
BATCH_SIZE=1

# Concurrent workers (limited for VPS resources)
CONCURRENT_WORKERS=1

# Retry attempts for failed operations
RETRY_ATTEMPTS=5

# Retry delay (increased for stability)
RETRY_DELAY=5s

# Batch upsert configuration (enabled for better performance)
CRAWLER_USE_UPSERT=true
CRAWLER_UPSERT_FALLBACK=true

# =============================================================================
# SCHEDULER CONFIGURATION (REAL-TIME MODE)
# =============================================================================

# Scheduler mode (realtime for live data)
SCHEDULER_MODE=realtime

# Enable real-time WebSocket monitoring
SCHEDULER_ENABLE_REALTIME=true

# Enable polling as fallback
SCHEDULER_ENABLE_POLLING=true

# Polling interval (conservative)
SCHEDULER_POLLING_INTERVAL=5s

# Fallback timeout (increased for stability)
SCHEDULER_FALLBACK_TIMEOUT=60s

# Reconnection attempts (increased for resilience)
SCHEDULER_RECONNECT_ATTEMPTS=10

# Reconnection delay (increased for stability)
SCHEDULER_RECONNECT_DELAY=10s

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Enable metrics collection
METRICS_ENABLED=true

# Health check interval (frequent for early detection)
HEALTH_CHECK_INTERVAL=15s

# =============================================================================
# NATS CONFIGURATION (IF USING MESSAGING)
# =============================================================================

# NATS server URL (uncomment if using NATS)
# NATS_URL=nats://nats:4222

# NATS connection timeout
# NATS_CONNECT_TIMEOUT=10s

# NATS reconnection attempts
# NATS_RECONNECT_ATTEMPTS=10

# NATS reconnection delay
# NATS_RECONNECT_DELAY=5s

# =============================================================================
# DOCKER RESOURCE LIMITS (FOR REFERENCE)
# =============================================================================
# These are not environment variables but recommended Docker resource limits:
#
# MongoDB Container:
# - Memory: 1GB limit, 512MB reservation
# - CPU: 1 core limit
#
# Scheduler Container:
# - Memory: 512MB limit, 256MB reservation
# - CPU: 0.5 core limit
#
# Network:
# - Use bridge network with proper DNS resolution
# - Enable container communication
#
# Volumes:
# - Persistent storage for MongoDB data
# - Log rotation for container logs

# =============================================================================
# TROUBLESHOOTING SETTINGS
# =============================================================================
# Uncomment these for debugging connection issues:

# Enable debug logging
# LOG_LEVEL=debug

# Increase timeouts for troubleshooting
# MONGO_CONNECT_TIMEOUT=30s
# ETHEREUM_REQUEST_TIMEOUT=180s
# SCHEDULER_FALLBACK_TIMEOUT=120s

# Reduce batch size for testing
# BATCH_SIZE=1
# CONCURRENT_WORKERS=1

# Enable verbose health checks
# HEALTH_CHECK_INTERVAL=10s
