package blockchain

import (
	"context"
	"fmt"
	"math/big"
	"strings"
	"time"

	"crypto-bubble-map-indexer/internal/domain/entity"
	"crypto-bubble-map-indexer/internal/domain/service"
	"crypto-bubble-map-indexer/internal/infrastructure/logger"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/crypto"
	"go.uber.org/zap"
)

// ERC20DecoderService implements enhanced ERC20 and contract decoder service
type ERC20DecoderService struct {
	logger     *logger.Logger
	classifier service.ContractClassifierService
}

// NewERC20DecoderService creates a new enhanced ERC20 decoder service
func NewERC20DecoderService(logger *logger.Logger) service.ERC20DecoderService {
	return &ERC20DecoderService{
		logger:     logger.WithComponent("erc20-decoder"),
		classifier: NewContractClassifierService(logger),
	}
}

// Common ERC20 function signatures
var (
	// ERC20 Standard Functions
	transferSignature     = "a9059cbb" // transfer(address,uint256)
	transferFromSignature = "23b872dd" // transferFrom(address,address,uint256)
	approveSignature      = "095ea7b3" // approve(address,uint256)

	// Extended ERC20 Functions
	increaseAllowanceSignature = "********" // increaseAllowance(address,uint256)
	decreaseAllowanceSignature = "a457c2d7" // decreaseAllowance(address,uint256)

	// Common DEX/DeFi Functions
	swapExactETHForTokensSignature    = "7ff36ab5" // Uniswap V2
	swapExactTokensForETHSignature    = "18cbafe5" // Uniswap V2
	swapExactTokensForTokensSignature = "38ed1739" // Uniswap V2
	addLiquiditySignature             = "e8e33700" // Uniswap V2
	removeLiquiditySignature          = "baa2abde" // Uniswap V2

	// Common patterns
	multicallSignature    = "ac9650d8" // Multicall
	safeTransferSignature = "42842e0e" // SafeTransfer
	depositSignature      = "d0e30db0" // deposit()
	withdrawSignature     = "2e1a7d4d" // withdraw(uint256)

	// Events
	transferEventSignature = crypto.Keccak256Hash([]byte("Transfer(address,address,uint256)"))
	approvalEventSignature = crypto.Keccak256Hash([]byte("Approval(address,address,uint256)"))
)

// DecodeERC20Transfer decodes ERC20 and contract interactions from transaction data
func (s *ERC20DecoderService) DecodeERC20Transfer(ctx context.Context, tx *entity.Transaction) ([]*entity.ERC20Transfer, error) {
	var transfers []*entity.ERC20Transfer

	s.logger.Debug("Attempting to decode contract interaction",
		zap.String("tx_hash", tx.Hash),
		zap.String("to", tx.To),
		zap.String("from", tx.From),
		zap.String("value", tx.Value),
		zap.String("data", tx.Data),
		zap.Int("data_length", len(tx.Data)))

	// Check if transaction has data (contract interaction)
	if tx.Data == "" || tx.Data == "0x" {
		s.logger.Debug("No transaction data found, creating ETH transfer record",
			zap.String("tx_hash", tx.Hash))
		// For ETH transfers, still create a record for tracking
		if tx.Value != "0" {
			transfer := s.createETHTransferRecord(tx)
			if transfer != nil {
				transfers = append(transfers, transfer)
			}
		}
		return transfers, nil
	}

	// Check if transaction is to a contract
	if tx.To == "" || tx.To == "******************************************" {
		s.logger.Debug("Transaction to zero address (contract creation), skipping",
			zap.String("tx_hash", tx.Hash))
		return transfers, nil
	}

	// Detect and decode contract interaction
	interactionType, decoded := s.decodeContractInteraction(tx)

	if decoded != nil {
		s.logger.Info("Successfully decoded contract interaction",
			zap.String("tx_hash", tx.Hash),
			zap.String("interaction_type", string(interactionType)),
			zap.String("contract", decoded.ContractAddress),
			zap.String("from", decoded.From),
			zap.String("to", decoded.To),
			zap.String("value", decoded.Value))
		transfers = append(transfers, decoded)
	} else {
		// Create fallback unknown contract interaction record
		s.logger.Info("Creating fallback contract interaction record",
			zap.String("tx_hash", tx.Hash),
			zap.String("contract", tx.To),
			zap.String("interaction_type", string(entity.InteractionUnknownContract)))

		fallback := s.createUnknownContractCallRecord(tx)
		if fallback != nil {
			transfers = append(transfers, fallback)
		}
	}

	return transfers, nil
}

// decodeContractInteraction decodes various types of contract interactions
func (s *ERC20DecoderService) decodeContractInteraction(tx *entity.Transaction) (entity.ContractInteractionType, *entity.ERC20Transfer) {
	data := tx.Data

	// Remove 0x prefix if present
	if strings.HasPrefix(data, "0x") {
		data = data[2:]
	}

	if len(data) < 8 {
		return entity.InteractionUnknownContract, nil
	}

	methodSig := strings.ToLower(data[:8])
	s.logger.Debug("Analyzing method signature",
		zap.String("tx_hash", tx.Hash),
		zap.String("method_sig", methodSig))

	switch methodSig {
	case transferSignature:
		transfer, err := s.decodeTransferMethod(tx, data)
		if err != nil {
			s.logger.Warn("Failed to decode transfer", zap.Error(err))
			return entity.InteractionTransfer, nil
		}
		return entity.InteractionTransfer, transfer

	case transferFromSignature:
		transfer, err := s.decodeTransferFromMethod(tx, data)
		if err != nil {
			s.logger.Warn("Failed to decode transferFrom", zap.Error(err))
			return entity.InteractionTransferFrom, nil
		}
		return entity.InteractionTransferFrom, transfer

	case approveSignature:
		transfer, err := s.decodeApprovalMethod(tx, data)
		if err != nil {
			s.logger.Warn("Failed to decode approve", zap.Error(err))
			return entity.InteractionApprove, nil
		}
		return entity.InteractionApprove, transfer

	case increaseAllowanceSignature:
		transfer, err := s.decodeAllowanceMethod(tx, data, "INCREASE")
		if err != nil {
			s.logger.Warn("Failed to decode increaseAllowance", zap.Error(err))
			return entity.InteractionIncreaseAllowance, nil
		}
		return entity.InteractionIncreaseAllowance, transfer

	case decreaseAllowanceSignature:
		transfer, err := s.decodeAllowanceMethod(tx, data, "DECREASE")
		if err != nil {
			s.logger.Warn("Failed to decode decreaseAllowance", zap.Error(err))
			return entity.InteractionDecreaseAllowance, nil
		}
		return entity.InteractionDecreaseAllowance, transfer

	case swapExactETHForTokensSignature, swapExactTokensForETHSignature, swapExactTokensForTokensSignature:
		transfer := s.createSwapRecord(tx, methodSig)
		return entity.InteractionSwap, transfer

	case addLiquiditySignature:
		transfer := s.createLiquidityRecord(tx, "ADD")
		return entity.InteractionAddLiquidity, transfer

	case removeLiquiditySignature:
		transfer := s.createLiquidityRecord(tx, "REMOVE")
		return entity.InteractionRemoveLiquidity, transfer

	case depositSignature:
		transfer := s.createDepositWithdrawRecord(tx, "DEPOSIT")
		return entity.InteractionDeposit, transfer

	case withdrawSignature:
		transfer := s.createDepositWithdrawRecord(tx, "WITHDRAW")
		return entity.InteractionWithdraw, transfer

	case multicallSignature:
		transfer := s.createMulticallRecord(tx)
		return entity.InteractionMulticall, transfer

	default:
		s.logger.Debug("Unknown method signature",
			zap.String("tx_hash", tx.Hash),
			zap.String("method_sig", methodSig))
		return entity.InteractionUnknownContract, nil
	}
}

// decodeDirectTransfer decodes direct ERC20 transfer function calls
func (s *ERC20DecoderService) decodeDirectTransfer(tx *entity.Transaction) (*entity.ERC20Transfer, error) {
	data := tx.Data

	s.logger.Debug("Decoding direct transfer",
		zap.String("tx_hash", tx.Hash),
		zap.String("original_data", data),
		zap.Int("data_length", len(data)))

	if len(data) < 10 { // 0x + 4 bytes method signature
		return nil, fmt.Errorf("data too short: %d characters", len(data))
	}

	// Remove 0x prefix if present
	if strings.HasPrefix(data, "0x") {
		data = data[2:]
	}

	s.logger.Debug("Processing data without 0x prefix",
		zap.String("tx_hash", tx.Hash),
		zap.String("processed_data", data),
		zap.Int("processed_length", len(data)))

	// Check if it's a transfer function call
	// transfer(address,uint256) method signature: 0xa9059cbb
	transferMethodSig := "a9059cbb"

	if len(data) < 8 {
		return nil, fmt.Errorf("data too short for method signature: %d characters", len(data))
	}

	methodSig := strings.ToLower(data[:8])
	if methodSig != transferMethodSig {
		s.logger.Debug("Not a transfer function call",
			zap.String("tx_hash", tx.Hash),
			zap.String("found_method_sig", methodSig),
			zap.String("expected_method_sig", transferMethodSig))
		return nil, fmt.Errorf("not a transfer function call: found %s, expected %s", methodSig, transferMethodSig)
	}

	s.logger.Debug("Found transfer method signature",
		zap.String("tx_hash", tx.Hash),
		zap.String("method_sig", methodSig))

	// Decode the function parameters
	// transfer(address to, uint256 value)
	// - to: 32 bytes (address padded)
	// - value: 32 bytes (uint256)

	if len(data) < 8+64+64 { // method sig + to address + value
		return nil, fmt.Errorf("insufficient data for transfer function: %d characters, need %d", len(data), 8+64+64)
	}

	// Extract 'to' address (bytes 8-72, take last 40 characters)
	toHex := strings.ToLower(data[8+24 : 8+64]) // Skip padding, get last 20 bytes
	toAddress := "0x" + toHex

	// Extract value (bytes 72-136)
	valueHex := data[8+64 : 8+128]
	value := new(big.Int)
	_, success := value.SetString(valueHex, 16)
	if !success {
		return nil, fmt.Errorf("failed to parse value hex: %s", valueHex)
	}

	s.logger.Debug("Parsed transfer parameters",
		zap.String("tx_hash", tx.Hash),
		zap.String("to_hex", toHex),
		zap.String("to_address", toAddress),
		zap.String("value_hex", valueHex),
		zap.String("value_decimal", value.String()))

	// Validate addresses
	if len(toHex) != 40 {
		return nil, fmt.Errorf("invalid to address length: %d characters", len(toHex))
	}

	// Create ERC20 transfer
	transfer := &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              toAddress,
		Value:           value.String(),
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
	}

	return transfer, nil
}

// decodeAlternativeFormats tries alternative ways to decode ERC20 transfers
func (s *ERC20DecoderService) decodeAlternativeFormats(tx *entity.Transaction) (*entity.ERC20Transfer, error) {
	data := tx.Data

	// Remove 0x prefix if present
	if strings.HasPrefix(data, "0x") {
		data = data[2:]
	}

	s.logger.Debug("Trying alternative decoding formats",
		zap.String("tx_hash", tx.Hash),
		zap.String("data", data))

	// Try detecting common ERC20 method signatures
	if len(data) >= 8 {
		methodSig := strings.ToLower(data[:8])
		s.logger.Debug("Detected method signature",
			zap.String("tx_hash", tx.Hash),
			zap.String("method_sig", methodSig))

		switch methodSig {
		case "a9059cbb": // transfer(address,uint256)
			return s.decodeTransferMethod(tx, data)
		case "23b872dd": // transferFrom(address,address,uint256)
			return s.decodeTransferFromMethod(tx, data)
		default:
			// Log unknown method signatures for debugging
			s.logger.Debug("Unknown method signature",
				zap.String("tx_hash", tx.Hash),
				zap.String("method_sig", methodSig))
		}
	}

	// If we have transaction value > 0 and it's sent to a contract,
	// we might want to create a basic transfer record for analysis
	if tx.Value != "0" && len(tx.To) == 42 {
		s.logger.Debug("Creating basic transfer record for contract interaction with value",
			zap.String("tx_hash", tx.Hash),
			zap.String("to", tx.To),
			zap.String("value", tx.Value))

		// This might be an ETH transfer to a contract that also handles tokens
		// For debugging purposes, we can track this
		transfer := &entity.ERC20Transfer{
			ContractAddress: tx.To,
			From:            tx.From,
			To:              tx.To,
			Value:           tx.Value,
			TxHash:          tx.Hash,
			BlockNumber:     tx.BlockNumber,
			Timestamp:       tx.Timestamp,
			Network:         tx.Network,
		}

		s.logger.Info("Created fallback transfer record",
			zap.String("tx_hash", tx.Hash),
			zap.String("contract", transfer.ContractAddress),
			zap.String("from", transfer.From),
			zap.String("to", transfer.To),
			zap.String("value", transfer.Value))

		return transfer, nil
	}

	return nil, fmt.Errorf("no alternative decoding method succeeded")
}

// decodeTransferMethod decodes ERC20 transfer function calls (enhanced)
func (s *ERC20DecoderService) decodeTransferMethod(tx *entity.Transaction, data string) (*entity.ERC20Transfer, error) {
	// transfer(address to, uint256 value)
	if len(data) < 8+64+64 {
		return nil, fmt.Errorf("insufficient data for transfer function: %d characters", len(data))
	}

	// Extract 'to' address (bytes 8-72, take last 40 characters)
	toHex := strings.ToLower(data[8+24 : 8+64])
	toAddress := "0x" + toHex

	// Extract value (bytes 72-136)
	valueHex := data[8+64 : 8+128]
	value := new(big.Int)
	_, success := value.SetString(valueHex, 16)
	if !success {
		return nil, fmt.Errorf("failed to parse value hex: %s", valueHex)
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              toAddress,
		Value:           value.String(),
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionTransfer,
		MethodSignature: transferSignature,
		Success:         true,
	}, nil
}

// decodeTransferFromMethod decodes ERC20 transferFrom function calls (enhanced)
func (s *ERC20DecoderService) decodeTransferFromMethod(tx *entity.Transaction, data string) (*entity.ERC20Transfer, error) {
	// transferFrom(address from, address to, uint256 value)
	if len(data) < 8+64+64+64 {
		return nil, fmt.Errorf("insufficient data for transferFrom function: %d characters", len(data))
	}

	// Extract 'from' address (bytes 8-72)
	fromHex := strings.ToLower(data[8+24 : 8+64])
	fromAddress := "0x" + fromHex

	// Extract 'to' address (bytes 72-136)
	toHex := strings.ToLower(data[8+64+24 : 8+128])
	toAddress := "0x" + toHex

	// Extract value (bytes 136-200)
	valueHex := data[8+128 : 8+192]
	value := new(big.Int)
	_, success := value.SetString(valueHex, 16)
	if !success {
		return nil, fmt.Errorf("failed to parse value hex: %s", valueHex)
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            fromAddress,
		To:              toAddress,
		Value:           value.String(),
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionTransferFrom,
		MethodSignature: transferFromSignature,
		Success:         true,
	}, nil
}

// decodeApprovalMethod decodes ERC20 approve function calls (enhanced)
func (s *ERC20DecoderService) decodeApprovalMethod(tx *entity.Transaction, data string) (*entity.ERC20Transfer, error) {
	// approve(address spender, uint256 value)
	if len(data) < 8+64+64 {
		return nil, fmt.Errorf("insufficient data for approve function: %d characters", len(data))
	}

	// Extract spender address (bytes 8-72, take last 40 characters)
	spenderHex := strings.ToLower(data[8+24 : 8+64])
	spenderAddress := "0x" + spenderHex

	// Extract value (bytes 72-136)
	valueHex := data[8+64 : 8+128]
	value := new(big.Int)
	_, success := value.SetString(valueHex, 16)
	if !success {
		return nil, fmt.Errorf("failed to parse value hex: %s", valueHex)
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              spenderAddress,
		Value:           value.String(),
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionApprove,
		MethodSignature: approveSignature,
		Success:         true,
	}, nil
}

// decodeAllowanceMethod decodes increaseAllowance/decreaseAllowance function calls (enhanced)
func (s *ERC20DecoderService) decodeAllowanceMethod(tx *entity.Transaction, data string, operation string) (*entity.ERC20Transfer, error) {
	// increaseAllowance(address spender, uint256 addedValue)
	// decreaseAllowance(address spender, uint256 subtractedValue)
	if len(data) < 8+64+64 {
		return nil, fmt.Errorf("insufficient data for %s allowance function: %d characters", operation, len(data))
	}

	// Extract spender address
	spenderHex := strings.ToLower(data[8+24 : 8+64])
	spenderAddress := "0x" + spenderHex

	// Extract value
	valueHex := data[8+64 : 8+128]
	value := new(big.Int)
	_, success := value.SetString(valueHex, 16)
	if !success {
		return nil, fmt.Errorf("failed to parse value hex: %s", valueHex)
	}

	var interactionType entity.ContractInteractionType
	var methodSig string
	if operation == "INCREASE" {
		interactionType = entity.InteractionIncreaseAllowance
		methodSig = increaseAllowanceSignature
	} else {
		interactionType = entity.InteractionDecreaseAllowance
		methodSig = decreaseAllowanceSignature
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              spenderAddress,
		Value:           value.String(),
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: interactionType,
		MethodSignature: methodSig,
		Success:         true,
	}, nil
}

// createSwapRecord creates a record for DEX swap operations
func (s *ERC20DecoderService) createSwapRecord(tx *entity.Transaction, methodSig string) *entity.ERC20Transfer {
	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionSwap,
		MethodSignature: methodSig,
		Success:         true,
	}
}

// createLiquidityRecord creates a record for liquidity operations
func (s *ERC20DecoderService) createLiquidityRecord(tx *entity.Transaction, operation string) *entity.ERC20Transfer {
	var interactionType entity.ContractInteractionType
	if operation == "ADD" {
		interactionType = entity.InteractionAddLiquidity
	} else {
		interactionType = entity.InteractionRemoveLiquidity
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: interactionType,
		MethodSignature: addLiquiditySignature, // Set appropriate signature
		Success:         true,
	}
}

// createDepositWithdrawRecord creates a record for deposit/withdraw operations
func (s *ERC20DecoderService) createDepositWithdrawRecord(tx *entity.Transaction, operation string) *entity.ERC20Transfer {
	var interactionType entity.ContractInteractionType
	var methodSig string
	if operation == "DEPOSIT" {
		interactionType = entity.InteractionDeposit
		methodSig = depositSignature
	} else {
		interactionType = entity.InteractionWithdraw
		methodSig = withdrawSignature
	}

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: interactionType,
		MethodSignature: methodSig,
		Success:         true,
	}
}

// createMulticallRecord creates a record for multicall operations
func (s *ERC20DecoderService) createMulticallRecord(tx *entity.Transaction) *entity.ERC20Transfer {
	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionMulticall,
		MethodSignature: multicallSignature,
		Success:         true,
	}
}

// createETHTransferRecord creates a record for pure ETH transfers
func (s *ERC20DecoderService) createETHTransferRecord(tx *entity.Transaction) *entity.ERC20Transfer {
	return &entity.ERC20Transfer{
		ContractAddress: "ETH",
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: entity.InteractionETHTransfer,
		MethodSignature: "ETH_TRANSFER",
		Success:         true,
	}
}

// createUnknownContractCallRecord creates a record for unknown contract calls
func (s *ERC20DecoderService) createUnknownContractCallRecord(tx *entity.Transaction) *entity.ERC20Transfer {
	// Extract method signature from transaction data
	methodSig := "UNKNOWN"
	if tx.Data != "" && tx.Data != "0x" && len(tx.Data) >= 10 {
		data := tx.Data
		if strings.HasPrefix(data, "0x") {
			data = data[2:]
		}
		if len(data) >= 8 {
			methodSig = strings.ToLower(data[:8])
		}
	}

	// Try to classify contract type from method signature
	contractType := s.classifier.ClassifyFromMethodSignature(methodSig)

	// Determine interaction type based on classification
	interactionType := entity.InteractionUnknownContract
	if contractType != entity.ContractTypeUnknown {
		// Map contract type to interaction type
		switch contractType {
		case entity.ContractTypeDEX, entity.ContractTypeAMM, entity.ContractTypeUniswapV2:
			interactionType = entity.InteractionSwap
		case entity.ContractTypeLendingPool, entity.ContractTypeCompound, entity.ContractTypeAave:
			if strings.Contains(strings.ToLower(methodSig), "withdraw") || methodSig == "2e1a7d4d" {
				interactionType = entity.InteractionWithdraw
			} else {
				interactionType = entity.InteractionDeposit
			}
		case entity.ContractTypeMulticall:
			interactionType = entity.InteractionMulticall
		}
	}

	s.logger.Debug("Contract classification from method signature",
		zap.String("tx_hash", tx.Hash),
		zap.String("method_sig", methodSig),
		zap.String("classified_type", string(contractType)),
		zap.String("interaction_type", string(interactionType)))

	return &entity.ERC20Transfer{
		ContractAddress: tx.To,
		From:            tx.From,
		To:              tx.To,
		Value:           tx.Value,
		TxHash:          tx.Hash,
		BlockNumber:     tx.BlockNumber,
		Timestamp:       tx.Timestamp,
		Network:         tx.Network,
		InteractionType: interactionType,
		MethodSignature: methodSig,
		Success:         true, // Assume success if transaction was mined
	}
}

// IsERC20Contract checks if an address is an ERC20 contract
func (s *ERC20DecoderService) IsERC20Contract(ctx context.Context, address string) (bool, error) {
	// Simplified check - in a real implementation, you would:
	// 1. Call the contract to check if it has ERC20 methods (balanceOf, transfer, etc.)
	// 2. Check if it emits Transfer events
	// 3. Maintain a cache/database of known ERC20 contracts

	// For now, we'll assume any contract address with sufficient transaction volume could be ERC20
	// This is a placeholder implementation
	return true, nil
}

// GetERC20ContractInfo retrieves ERC20 contract information
func (s *ERC20DecoderService) GetERC20ContractInfo(ctx context.Context, address string) (*entity.ERC20Contract, error) {
	// In a real implementation, you would:
	// 1. Call the contract's name(), symbol(), decimals() methods
	// 2. Cache the results
	// 3. Return the contract info

	// Placeholder implementation
	contract := &entity.ERC20Contract{
		Address:   address,
		Name:      "Unknown Token",
		Symbol:    "UNK",
		Decimals:  18,
		FirstSeen: time.Now(),
		LastSeen:  time.Now(),
		TotalTxs:  1,
		Network:   "ethereum",
	}

	return contract, nil
}

// getERC20ABI returns the ERC20 ABI for decoding
func getERC20ABI() abi.ABI {
	// Standard ERC20 ABI
	erc20ABI := `[
		{
			"constant": false,
			"inputs": [
				{"name": "_to", "type": "address"},
				{"name": "_value", "type": "uint256"}
			],
			"name": "transfer",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		},
		{
			"constant": false,
			"inputs": [
				{"name": "_from", "type": "address"},
				{"name": "_to", "type": "address"},
				{"name": "_value", "type": "uint256"}
			],
			"name": "transferFrom",
			"outputs": [{"name": "", "type": "bool"}],
			"type": "function"
		}
	]`

	erc20ABI_parsed, _ := abi.JSON(strings.NewReader(erc20ABI))
	return erc20ABI_parsed
}
