# Ethereum RPC Configuration
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/********************************
ETHEREUM_WS_URL=wss://mainnet.infura.io/v3/********************************
START_BLOCK_NUMBER=22759500

# MongoDB Configuration
MONGO_URI=mongodb+srv://haitranwang:<EMAIL>/
MONGO_DATABASE=ethereum_raw_data
MONGO_CONNECT_TIMEOUT=10s
MONGO_MAX_POOL_SIZE=100

# Application Configuration
APP_PORT=8080
APP_ENV=development
LOG_LEVEL=info

# Crawler Configuration - Very conservative
BATCH_SIZE=1
CONCURRENT_WORKERS=1
RETRY_ATTEMPTS=3
RETRY_DELAY=2s

# Batch Upsert Configuration - Enable duplicate handling
CRAWLER_USE_UPSERT=true
CRAWLER_UPSERT_FALLBACK=true

# Rate limiting for Ethereum API
ETHEREUM_RATE_LIMIT=500ms
ETHEREUM_REQUEST_TIMEOUT=60s
ETHEREUM_SKIP_RECEIPTS=false

# Scheduler Configuration - Hybrid mode with real-time and polling
SCHEDULER_MODE=hybrid
SCHEDULER_ENABLE_REALTIME=true
SCHEDULER_ENABLE_POLLING=true
SCHEDULER_POLLING_INTERVAL=3s
SCHEDULER_FALLBACK_TIMEOUT=30s
SCHEDULER_RECONNECT_ATTEMPTS=5
SCHEDULER_RECONNECT_DELAY=5s
SCHEDULER_MAX_RETRIES=3
SCHEDULER_SKIP_DURATION=30s

# WebSocket Configuration
WEBSOCKET_RECONNECT_ATTEMPTS=5
WEBSOCKET_RECONNECT_DELAY=5s
WEBSOCKET_PING_INTERVAL=30s

# GraphQL Configuration
GRAPHQL_ENDPOINT=/graphql
GRAPHQL_PLAYGROUND=true

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_INTERVAL=30s

# NATS JetStream Configuration
NATS_URL=nats://localhost:4222
NATS_STREAM_NAME=TRANSACTIONS
NATS_SUBJECT_PREFIX=transactions
NATS_CONNECT_TIMEOUT=10s
NATS_RECONNECT_ATTEMPTS=5
NATS_RECONNECT_DELAY=2s
NATS_MAX_PENDING_MESSAGES=1000
NATS_ENABLED=true
