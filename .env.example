# Server Configuration
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
GIN_MODE=release
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://crypto-bubble-map.com

# Neo4j Configuration (Graph Database)
NEO4J_URI=neo4j://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password
NEO4J_DATABASE=neo4j
NEO4J_MAX_CONNECTION_POOL_SIZE=50
NEO4J_CONNECTION_TIMEOUT=30s

# MongoDB Configuration (Raw Transaction Data)
MONGO_URI=mongodb://localhost:27017/ethereum_raw_data
MONGO_MAX_POOL_SIZE=20
MONGO_MIN_POOL_SIZE=5
MONGO_CONNECTION_TIMEOUT=30s

# PostgreSQL Configuration (User Data, Watch Lists, Reports)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=crypto_bubble_map
POSTGRES_SSL_MODE=disable
POSTGRES_MAX_OPEN_CONNS=25
POSTGRES_MAX_IDLE_CONNS=5
POSTGRES_CONN_MAX_LIFETIME=5m

# Redis Configuration (Caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_POOL_SIZE=10
REDIS_MIN_IDLE_CONNS=5

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=168h

# Application Configuration
APP_ENV=development
LOG_LEVEL=info
DEBUG=false

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST=20

# Cache Configuration
CACHE_TTL_WALLET_NETWORK=300s
CACHE_TTL_WALLET_RANKINGS=600s
CACHE_TTL_DASHBOARD_STATS=180s
CACHE_TTL_RISK_SCORES=900s

# External APIs
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
COINGECKO_API_KEY=your-coingecko-api-key

# Monitoring & Observability
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=false
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# Security
ENABLE_CORS=true
ENABLE_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true
MAX_REQUEST_SIZE=10MB

# GraphQL Configuration
GRAPHQL_PLAYGROUND_ENABLED=true
GRAPHQL_INTROSPECTION_ENABLED=true
GRAPHQL_COMPLEXITY_LIMIT=1000
GRAPHQL_DEPTH_LIMIT=15

# Background Jobs
ENABLE_BACKGROUND_JOBS=true
RISK_SCORE_UPDATE_INTERVAL=1h
WALLET_STATS_UPDATE_INTERVAL=30m
CACHE_CLEANUP_INTERVAL=6h
