# GraphQL schema location
schema:
  - graph/schema.graphqls

# Where should the generated server code go?
exec:
  filename: graph/generated/generated.go
  package: generated

# Where should any generated models go?
model:
  filename: graph/model/models_gen.go
  package: model

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: graph
  package: graph
  filename_template: "{name}.resolvers.go"

# Optional: turn on/off introspection. When disabled, clients will not be able to query the server for the GraphQL schema.
introspection: true

# Optional: turn on/off the playground. When disabled, the playground will not be available.
playground: true

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
  - "crypto-bubble-map-be/internal/domain/entity"

# This section declares type mapping between the GraphQL and Go type systems
models:
  # Scalars
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Time:
    model:
      - time.Time
  JSON:
    model:
      - map[string]interface{}
  BigInt:
    model:
      - math/big.Int

  # Entity mappings
  Wallet:
    model: crypto-bubble-map-be/internal/domain/entity.Wallet
  WalletConnection:
    model: crypto-bubble-map-be/internal/domain/entity.WalletConnection
  WalletNetwork:
    model: crypto-bubble-map-be/internal/domain/entity.WalletNetwork
  Transaction:
    model: crypto-bubble-map-be/internal/domain/entity.Transaction
  PairwiseTransaction:
    model: crypto-bubble-map-be/internal/domain/entity.PairwiseTransaction
  MoneyFlowData:
    model: crypto-bubble-map-be/internal/domain/entity.MoneyFlowData
  RiskScore:
    model: crypto-bubble-map-be/internal/domain/entity.RiskScore
  NetworkInfo:
    model: crypto-bubble-map-be/internal/domain/entity.NetworkInfo
  NetworkStats:
    model: crypto-bubble-map-be/internal/domain/entity.NetworkStats
  DashboardStats:
    model: crypto-bubble-map-be/internal/domain/entity.DashboardStats
  WalletRanking:
    model: crypto-bubble-map-be/internal/domain/entity.WalletRanking
  WatchedWallet:
    model: crypto-bubble-map-be/internal/domain/entity.WatchedWallet
  WalletAlert:
    model: crypto-bubble-map-be/internal/domain/entity.WalletAlert
  SecurityAlert:
    model: crypto-bubble-map-be/internal/domain/entity.SecurityAlert
  AIResponse:
    model: crypto-bubble-map-be/internal/domain/entity.AIResponse

  # Enums
  WalletType:
    model: crypto-bubble-map-be/internal/domain/entity.WalletType
  RiskLevel:
    model: crypto-bubble-map-be/internal/domain/entity.RiskLevel
  TransactionType:
    model: crypto-bubble-map-be/internal/domain/entity.TransactionType
  TransactionStatus:
    model: crypto-bubble-map-be/internal/domain/entity.TransactionStatus
  AlertType:
    model: crypto-bubble-map-be/internal/domain/entity.AlertType
  AlertSeverity:
    model: crypto-bubble-map-be/internal/domain/entity.AlertSeverity
  RankingCategory:
    model: crypto-bubble-map-be/internal/domain/entity.RankingCategory

# Optional: set to speed up generation time by not performing a final validation pass.
skip_validation: false

# Optional: set to skip running `go mod tidy` when generating server code
skip_mod_tidy: false
